# PDF to OCR Text Converter

Convert your PDF books into searchable, editable text using Optical Character Recognition (OCR).

## 📋 What This Does

This tool converts your "Dogars ECAT Book.pdf" (or any PDF) into:
- **Plain text file** (.txt) - Easy to search and edit
- **Markdown file** (.md) - Formatted for better readability
- **Individual page files** (optional) - Separate text file for each page

## 🚀 Quick Start

### Option 1: Automatic Setup (Recommended)
1. Run the setup script:
   ```bash
   python setup_ocr.py
   ```

2. Convert your book:
   ```bash
   python pdf_to_ocr.py "Dogars ECAT Book.pdf"
   ```

### Option 2: Windows Batch File
1. Double-click `convert_book.bat`
2. Wait for conversion to complete

## 📦 Manual Installation

If the automatic setup doesn't work, install manually:

### 1. Install Python Dependencies
```bash
pip install pdf2image pytesseract Pillow
```

### 2. Install Tesseract OCR

**Windows:**
1. Download from: https://github.com/UB-Mannheim/tesseract/wiki
2. Install the executable
3. Add to PATH: `C:\Program Files\Tesseract-OCR`

**macOS:**
```bash
brew install tesseract
```

**Linux (Ubuntu/Debian):**
```bash
sudo apt-get install tesseract-ocr
```

## 🔧 Usage Options

### Basic Usage
```bash
python pdf_to_ocr.py "Dogars ECAT Book.pdf"
```

### Advanced Options
```bash
python pdf_to_ocr.py "Dogars ECAT Book.pdf" \
    --output-dir ./ocr_output \
    --language eng \
    --dpi 300 \
    --individual-pages
```

### Parameters:
- `--output-dir`: Where to save output files
- `--language`: OCR language (eng, ara, urd, etc.)
- `--dpi`: Image quality (higher = better accuracy, slower)
- `--individual-pages`: Save separate file for each page

## 📄 Output Files

After conversion, you'll get:

1. **`Dogars ECAT Book_ocr.txt`** - Complete book as plain text
2. **`Dogars ECAT Book_ocr.md`** - Formatted markdown version
3. **`Dogars ECAT Book_page_001.txt`** - Individual pages (if requested)

## 🎯 Tips for Better Results

1. **Higher DPI = Better Quality**
   - Use `--dpi 400` for better text recognition
   - Trade-off: Higher DPI takes longer

2. **Language Support**
   - For Urdu text: `--language urd`
   - For Arabic text: `--language ara`
   - Multiple languages: `--language eng+urd`

3. **Large Files**
   - Process may take several minutes for large books
   - Use `--individual-pages` to process in chunks

## 🔍 Troubleshooting

### "Tesseract not found"
- Make sure Tesseract is installed and in your PATH
- Windows: Add `C:\Program Files\Tesseract-OCR` to PATH

### "pdf2image error"
- Install poppler-utils (Linux) or poppler (macOS)
- Windows: pdf2image should work out of the box

### Poor OCR Quality
- Increase DPI: `--dpi 400` or `--dpi 600`
- Ensure PDF has good image quality
- Try different language settings

### Memory Issues
- Use `--individual-pages` for large files
- Close other applications to free memory

## 📊 Expected Results

- **Processing Time**: 1-3 minutes per page (depending on DPI)
- **Accuracy**: 85-95% for clear text
- **File Size**: Text files are much smaller than original PDF

## 🆘 Need Help?

If you encounter issues:
1. Check that all dependencies are installed
2. Verify Tesseract is in your PATH
3. Try with a smaller PDF first
4. Check the log messages for specific errors

## 📝 Example Output Structure

```
Dogars ECAT Book_ocr.txt:
==================================================
PAGE 1
==================================================

[Extracted text from page 1]

==================================================
PAGE 2
==================================================

[Extracted text from page 2]
...
```

Happy reading! 📚✨
