#!/usr/bin/env python3
"""
PDF to OCR Text Converter
Converts PDF files to searchable text using OCR (Optical Character Recognition)
"""

import os
import sys
from pathlib import Path
import argparse
from typing import List, Optional
import logging

try:
    from pdf2image import convert_from_path
    import pytesseract
    from PIL import Image

    # Set Tesseract path for Windows
    if os.name == 'nt':  # Windows
        tesseract_path = r"C:\Program Files\Tesseract-OCR\tesseract.exe"
        if os.path.exists(tesseract_path):
            pytesseract.pytesseract.tesseract_cmd = tesseract_path

        # Set poppler path for Windows
        poppler_path = os.path.expandvars(r"$LOCALAPPDATA\Microsoft\WinGet\Packages\oschwartz10612.Poppler_Microsoft.Winget.Source_8wekyb3d8bbwe\poppler-24.08.0\Library\bin")
        if os.path.exists(poppler_path):
            os.environ["PATH"] = poppler_path + os.pathsep + os.environ.get("PATH", "")

except ImportError as e:
    print(f"Missing required library: {e}")
    print("Please install required packages:")
    print("pip install pdf2image pytesseract Pillow")
    sys.exit(1)

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class PDFOCRConverter:
    """Convert PDF files to OCR text"""
    
    def __init__(self, pdf_path: str, output_dir: Optional[str] = None, language: str = 'eng'):
        """
        Initialize the PDF OCR converter
        
        Args:
            pdf_path: Path to the PDF file
            output_dir: Directory to save output files (default: same as PDF)
            language: OCR language code (default: 'eng' for English)
        """
        self.pdf_path = Path(pdf_path)
        self.output_dir = Path(output_dir) if output_dir else self.pdf_path.parent
        self.language = language
        
        # Validate inputs
        if not self.pdf_path.exists():
            raise FileNotFoundError(f"PDF file not found: {self.pdf_path}")
        
        # Create output directory if it doesn't exist
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Set output file paths
        self.text_output = self.output_dir / f"{self.pdf_path.stem}_ocr.txt"
        self.markdown_output = self.output_dir / f"{self.pdf_path.stem}_ocr.md"
    
    def convert_pdf_to_images(self, dpi: int = 300) -> List[Image.Image]:
        """
        Convert PDF pages to images
        
        Args:
            dpi: Resolution for image conversion (higher = better quality, slower)
            
        Returns:
            List of PIL Image objects
        """
        logger.info(f"Converting PDF to images with {dpi} DPI...")
        try:
            images = convert_from_path(
                self.pdf_path,
                dpi=dpi,
                fmt='PNG',
                thread_count=4  # Use multiple threads for faster conversion
            )
            logger.info(f"Successfully converted {len(images)} pages to images")
            return images
        except Exception as e:
            logger.error(f"Error converting PDF to images: {e}")
            raise
    
    def extract_text_from_image(self, image: Image.Image, page_num: int) -> str:
        """
        Extract text from a single image using OCR
        
        Args:
            image: PIL Image object
            page_num: Page number for logging
            
        Returns:
            Extracted text
        """
        try:
            # Configure Tesseract for better accuracy
            custom_config = r'--oem 3 --psm 6 -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz.,!?;:()[]{}"\'-+*/=<>@#$%^&_|\\~`'
            
            text = pytesseract.image_to_string(
                image,
                lang=self.language,
                config=custom_config
            )
            
            logger.info(f"Extracted text from page {page_num} ({len(text)} characters)")
            return text.strip()
            
        except Exception as e:
            logger.error(f"Error extracting text from page {page_num}: {e}")
            return f"[ERROR: Could not extract text from page {page_num}]"
    
    def convert_to_text(self, dpi: int = 300, save_individual_pages: bool = False) -> str:
        """
        Convert entire PDF to text using OCR
        
        Args:
            dpi: Image resolution for OCR
            save_individual_pages: Whether to save individual page text files
            
        Returns:
            Complete extracted text
        """
        logger.info(f"Starting OCR conversion of {self.pdf_path}")
        
        # Convert PDF to images
        images = self.convert_pdf_to_images(dpi)
        
        # Extract text from each page
        all_text = []
        page_texts = []
        
        for i, image in enumerate(images, 1):
            logger.info(f"Processing page {i}/{len(images)}")
            
            page_text = self.extract_text_from_image(image, i)
            page_texts.append(page_text)
            
            # Add page separator and text
            page_header = f"\n{'='*50}\nPAGE {i}\n{'='*50}\n\n"
            all_text.append(page_header + page_text)
            
            # Save individual page if requested
            if save_individual_pages:
                page_file = self.output_dir / f"{self.pdf_path.stem}_page_{i:03d}.txt"
                with open(page_file, 'w', encoding='utf-8') as f:
                    f.write(page_text)
        
        complete_text = '\n\n'.join(all_text)
        
        # Save complete text file
        logger.info(f"Saving complete OCR text to {self.text_output}")
        with open(self.text_output, 'w', encoding='utf-8') as f:
            f.write(complete_text)
        
        # Save as markdown with better formatting
        self.save_as_markdown(page_texts)
        
        logger.info(f"OCR conversion completed successfully!")
        logger.info(f"Text file: {self.text_output}")
        logger.info(f"Markdown file: {self.markdown_output}")
        
        return complete_text
    
    def save_as_markdown(self, page_texts: List[str]):
        """Save the OCR text as a formatted markdown file"""
        markdown_content = f"# {self.pdf_path.stem} - OCR Text\n\n"
        markdown_content += f"*Converted from PDF using OCR*\n\n"
        markdown_content += f"**Total Pages:** {len(page_texts)}\n\n"
        markdown_content += "---\n\n"
        
        for i, text in enumerate(page_texts, 1):
            markdown_content += f"## Page {i}\n\n"
            markdown_content += text + "\n\n"
            markdown_content += "---\n\n"
        
        with open(self.markdown_output, 'w', encoding='utf-8') as f:
            f.write(markdown_content)

def main():
    """Main function to run the PDF OCR converter"""
    parser = argparse.ArgumentParser(description='Convert PDF to OCR text')
    parser.add_argument('pdf_path', help='Path to the PDF file')
    parser.add_argument('--output-dir', '-o', help='Output directory (default: same as PDF)')
    parser.add_argument('--language', '-l', default='eng', help='OCR language code (default: eng)')
    parser.add_argument('--dpi', type=int, default=300, help='Image DPI for OCR (default: 300)')
    parser.add_argument('--individual-pages', action='store_true', help='Save individual page text files')
    
    args = parser.parse_args()
    
    try:
        converter = PDFOCRConverter(
            pdf_path=args.pdf_path,
            output_dir=args.output_dir,
            language=args.language
        )
        
        converter.convert_to_text(
            dpi=args.dpi,
            save_individual_pages=args.individual_pages
        )
        
        print(f"\n✅ OCR conversion completed successfully!")
        print(f"📄 Text file: {converter.text_output}")
        print(f"📝 Markdown file: {converter.markdown_output}")
        
    except Exception as e:
        logger.error(f"Conversion failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
