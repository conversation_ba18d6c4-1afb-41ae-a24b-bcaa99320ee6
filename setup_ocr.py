#!/usr/bin/env python3
"""
Setup script for PDF OCR converter
Installs required dependencies and checks system requirements
"""

import subprocess
import sys
import platform
import os
from pathlib import Path

def run_command(command, description):
    """Run a command and handle errors"""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed: {e}")
        if e.stdout:
            print(f"stdout: {e.stdout}")
        if e.stderr:
            print(f"stderr: {e.stderr}")
        return False

def check_python_version():
    """Check if Python version is compatible"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 7):
        print(f"❌ Python 3.7+ required, found {version.major}.{version.minor}")
        return False
    print(f"✅ Python {version.major}.{version.minor}.{version.micro} is compatible")
    return True

def install_tesseract():
    """Install Tesseract OCR based on the operating system"""
    system = platform.system().lower()
    
    print(f"🔍 Detected operating system: {system}")
    
    if system == "windows":
        print("📋 For Windows, please install Tesseract manually:")
        print("1. Download from: https://github.com/UB-Mannheim/tesseract/wiki")
        print("2. Install the executable")
        print("3. Add Tesseract to your PATH environment variable")
        print("4. Common installation path: C:\\Program Files\\Tesseract-OCR")
        return True
        
    elif system == "darwin":  # macOS
        print("🍎 Installing Tesseract on macOS...")
        if run_command("brew --version", "Checking Homebrew"):
            return run_command("brew install tesseract", "Installing Tesseract via Homebrew")
        else:
            print("❌ Homebrew not found. Please install Homebrew first:")
            print("Visit: https://brew.sh/")
            return False
            
    elif system == "linux":
        print("🐧 Installing Tesseract on Linux...")
        # Try different package managers
        if run_command("which apt-get", "Checking apt-get"):
            return run_command("sudo apt-get update && sudo apt-get install -y tesseract-ocr", 
                             "Installing Tesseract via apt-get")
        elif run_command("which yum", "Checking yum"):
            return run_command("sudo yum install -y tesseract", "Installing Tesseract via yum")
        elif run_command("which dnf", "Checking dnf"):
            return run_command("sudo dnf install -y tesseract", "Installing Tesseract via dnf")
        else:
            print("❌ No supported package manager found")
            print("Please install Tesseract manually for your Linux distribution")
            return False
    else:
        print(f"❌ Unsupported operating system: {system}")
        return False

def install_python_packages():
    """Install required Python packages"""
    print("📦 Installing Python packages...")
    return run_command(f"{sys.executable} -m pip install -r requirements.txt", 
                      "Installing Python dependencies")

def test_installation():
    """Test if all components are working"""
    print("🧪 Testing installation...")
    
    try:
        import pdf2image
        import pytesseract
        from PIL import Image
        print("✅ All Python packages imported successfully")
        
        # Test Tesseract
        version = pytesseract.get_tesseract_version()
        print(f"✅ Tesseract version: {version}")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Tesseract test failed: {e}")
        print("💡 Make sure Tesseract is installed and in your PATH")
        return False

def main():
    """Main setup function"""
    print("🚀 Setting up PDF OCR Converter")
    print("=" * 50)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Install Python packages
    if not install_python_packages():
        print("❌ Failed to install Python packages")
        sys.exit(1)
    
    # Install Tesseract
    if not install_tesseract():
        print("❌ Failed to install Tesseract")
        print("💡 You may need to install Tesseract manually")
    
    # Test installation
    if test_installation():
        print("\n🎉 Setup completed successfully!")
        print("You can now run: python pdf_to_ocr.py 'Dogars ECAT Book.pdf'")
    else:
        print("\n❌ Setup completed with errors")
        print("Please check the error messages above and resolve any issues")

if __name__ == "__main__":
    main()
